using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;

namespace EconomySystem
{
    /// <summary>
    /// Manager quản lý hệ thống cửa hàng
    /// Tích hợp với CurrencyManager và InventoryManager
    /// </summary>
    public class ShopManager : MonoBehaviour
    {
        #region Singleton
        private static ShopManager _instance;
        public static ShopManager Instance
        {
            get
            {
                if (_instance == null)
                {
                    _instance = FindObjectOfType<ShopManager>();
                    if (_instance == null)
                    {
                        GameObject go = new GameObject("ShopManager");
                        _instance = go.AddComponent<ShopManager>();
                        DontDestroyOnLoad(go);
                    }
                }
                return _instance;
            }
        }
        #endregion

        #region Events
        /// <summary>
        /// Event khi mua vật phẩm thành công (item, số lượng)
        /// </summary>
        public static event Action<Item, int> OnItemPurchased;

        /// <summary>
        /// Event khi bán vật phẩm thành công (item, số lượng)
        /// </summary>
        public static event Action<Item, int> OnItemSold;

        /// <summary>
        /// Event khi giao dịch thất bại (lý do)
        /// </summary>
        public static event Action<string> OnTransactionFailed;

        /// <summary>
        /// Event khi danh sách shop thay đổi
        /// </summary>
        public static event Action OnShopUpdated;
        #endregion

        #region Fields
        [Header("Cài Đặt Shop")]
        [SerializeField] private ItemDatabase itemDatabase;
        [SerializeField] private bool hienThiLog = true;
        [SerializeField] private float phanTramGiamGiaBan = 0.5f; // Bán được 50% giá mua

        [Header("Bộ Lọc")]
        [SerializeField] private ItemType boLocLoai = ItemType.VatLieu;
        [SerializeField] private string tuKhoaTimKiem = "";
        [SerializeField] private bool chiHienThiVatPhamMoi = false;
        [SerializeField] private bool chiHienThiVatPhamNoiBat = false;
        #endregion

        #region Properties
        /// <summary>
        /// Database vật phẩm
        /// </summary>
        public ItemDatabase ItemDatabase 
        { 
            get => itemDatabase; 
            set 
            { 
                itemDatabase = value;
                OnShopUpdated?.Invoke();
            } 
        }

        /// <summary>
        /// Phần trăm giảm giá khi bán (0.5 = bán được 50% giá mua)
        /// </summary>
        public float PhanTramGiamGiaBan 
        { 
            get => phanTramGiamGiaBan; 
            set => phanTramGiamGiaBan = Mathf.Clamp01(value); 
        }
        #endregion

        #region Unity Methods
        private void Awake()
        {
            if (_instance == null)
            {
                _instance = this;
                DontDestroyOnLoad(gameObject);
            }
            else if (_instance != this)
            {
                Destroy(gameObject);
            }
        }

        private void Start()
        {
            if (itemDatabase == null)
            {
                Debug.LogWarning("ItemDatabase chưa được gán cho ShopManager!");
            }
        }
        #endregion

        #region Public Methods - Mua Bán
        /// <summary>
        /// Mua vật phẩm từ cửa hàng
        /// </summary>
        /// <param name="itemID">ID vật phẩm</param>
        /// <param name="soLuong">Số lượng muốn mua</param>
        /// <returns>True nếu mua thành công</returns>
        public bool MuaVatPham(string itemID, int soLuong = 1)
        {
            if (itemDatabase == null)
            {
                Debug.LogError("ItemDatabase không được gán!");
                OnTransactionFailed?.Invoke("Lỗi hệ thống cửa hàng");
                return false;
            }

            var item = itemDatabase.LayVatPhamTheoID(itemID);
            if (item == null)
            {
                if (hienThiLog)
                    Debug.LogWarning($"Không tìm thấy vật phẩm với ID: {itemID}");
                OnTransactionFailed?.Invoke("Vật phẩm không tồn tại");
                return false;
            }

            return MuaVatPham(item, soLuong);
        }

        /// <summary>
        /// Mua vật phẩm từ cửa hàng
        /// </summary>
        /// <param name="item">Vật phẩm muốn mua</param>
        /// <param name="soLuong">Số lượng muốn mua</param>
        /// <returns>True nếu mua thành công</returns>
        public bool MuaVatPham(Item item, int soLuong = 1)
        {
            if (item == null || soLuong <= 0)
            {
                OnTransactionFailed?.Invoke("Thông tin mua hàng không hợp lệ");
                return false;
            }

            // Kiểm tra có thể mua không
            if (!CoTheMuaVatPham(item, soLuong))
                return false;

            int tongTien = item.GiaMua * soLuong;

            // Trừ tiền
            if (!CurrencyManager.Instance.TruTien(tongTien, $"Mua {soLuong}x {item.TenVatPham}"))
            {
                OnTransactionFailed?.Invoke("Không đủ tiền");
                return false;
            }

            // Thêm vào inventory
            int soLuongThucTeThemVao = InventoryManager.Instance.ThemVatPham(item, soLuong);

            if (soLuongThucTeThemVao < soLuong)
            {
                // Hoàn tiền cho phần không thể thêm vào inventory
                int soTienHoan = (soLuong - soLuongThucTeThemVao) * item.GiaMua;
                CurrencyManager.Instance.ThemTien(soTienHoan, "Hoàn tiền do inventory đầy");

                if (hienThiLog)
                    Debug.LogWarning($"Chỉ mua được {soLuongThucTeThemVao}/{soLuong} do inventory đầy");
            }

            if (soLuongThucTeThemVao > 0)
            {
                if (hienThiLog)
                    Debug.Log($"Đã mua {soLuongThucTeThemVao}x {item.TenVatPham} với giá {item.GiaMua * soLuongThucTeThemVao} Lea");

                OnItemPurchased?.Invoke(item, soLuongThucTeThemVao);
                return true;
            }

            return false;
        }

        /// <summary>
        /// Bán vật phẩm cho cửa hàng
        /// </summary>
        /// <param name="itemID">ID vật phẩm</param>
        /// <param name="soLuong">Số lượng muốn bán</param>
        /// <returns>True nếu bán thành công</returns>
        public bool BanVatPham(string itemID, int soLuong = 1)
        {
            if (itemDatabase == null)
            {
                Debug.LogError("ItemDatabase không được gán!");
                OnTransactionFailed?.Invoke("Lỗi hệ thống cửa hàng");
                return false;
            }

            var item = itemDatabase.LayVatPhamTheoID(itemID);
            if (item == null)
            {
                if (hienThiLog)
                    Debug.LogWarning($"Không tìm thấy vật phẩm với ID: {itemID}");
                OnTransactionFailed?.Invoke("Vật phẩm không tồn tại");
                return false;
            }

            return BanVatPham(item, soLuong);
        }

        /// <summary>
        /// Bán vật phẩm cho cửa hàng
        /// </summary>
        /// <param name="item">Vật phẩm muốn bán</param>
        /// <param name="soLuong">Số lượng muốn bán</param>
        /// <returns>True nếu bán thành công</returns>
        public bool BanVatPham(Item item, int soLuong = 1)
        {
            if (item == null || soLuong <= 0)
            {
                OnTransactionFailed?.Invoke("Thông tin bán hàng không hợp lệ");
                return false;
            }

            // Kiểm tra có thể bán không
            if (!CoTheBanVatPham(item, soLuong))
                return false;

            // Xóa khỏi inventory
            int soLuongThucTeXoa = InventoryManager.Instance.XoaVatPham(item.ID, soLuong);

            if (soLuongThucTeXoa > 0)
            {
                // Tính tiền bán
                int tienBan = Mathf.RoundToInt(item.GiaBan * soLuongThucTeXoa);
                CurrencyManager.Instance.ThemTien(tienBan, $"Bán {soLuongThucTeXoa}x {item.TenVatPham}");

                if (hienThiLog)
                    Debug.Log($"Đã bán {soLuongThucTeXoa}x {item.TenVatPham} với giá {tienBan} Lea");

                OnItemSold?.Invoke(item, soLuongThucTeXoa);
                return true;
            }

            return false;
        }

        /// <summary>
        /// Kiểm tra có thể mua vật phẩm không
        /// </summary>
        /// <param name="item">Vật phẩm</param>
        /// <param name="soLuong">Số lượng</param>
        /// <returns>True nếu có thể mua</returns>
        public bool CoTheMuaVatPham(Item item, int soLuong = 1)
        {
            if (item == null || soLuong <= 0)
                return false;

            int tongTien = item.GiaMua * soLuong;

            // Kiểm tra đủ tiền
            if (!CurrencyManager.Instance.KiemTraDuTien(tongTien))
            {
                OnTransactionFailed?.Invoke($"Không đủ tiền! Cần {tongTien} Lea");
                return false;
            }

            return true;
        }

        /// <summary>
        /// Kiểm tra có thể bán vật phẩm không
        /// </summary>
        /// <param name="item">Vật phẩm</param>
        /// <param name="soLuong">Số lượng</param>
        /// <returns>True nếu có thể bán</returns>
        public bool CoTheBanVatPham(Item item, int soLuong = 1)
        {
            if (item == null || soLuong <= 0)
                return false;

            // Kiểm tra vật phẩm có thể bán không
            if (!item.CoTheBan)
            {
                OnTransactionFailed?.Invoke("Vật phẩm này không thể bán");
                return false;
            }

            // Kiểm tra có đủ vật phẩm trong inventory không
            if (!InventoryManager.Instance.CoVatPham(item.ID, soLuong))
            {
                OnTransactionFailed?.Invoke($"Không đủ {item.TenVatPham} để bán");
                return false;
            }

            return true;
        }
        #endregion

        #region Public Methods - Lấy Dữ Liệu
        /// <summary>
        /// Lấy danh sách vật phẩm bán trong cửa hàng
        /// </summary>
        /// <returns>Danh sách vật phẩm</returns>
        public List<Item> LayDanhSachVatPhamBan()
        {
            if (itemDatabase == null)
                return new List<Item>();

            var danhSach = itemDatabase.LayDanhSachVatPhamBan();

            // Áp dụng bộ lọc
            if (!string.IsNullOrEmpty(tuKhoaTimKiem))
            {
                danhSach = itemDatabase.TimKiemVatPham(tuKhoaTimKiem);
            }

            if (chiHienThiVatPhamMoi)
            {
                danhSach = itemDatabase.LayVatPhamMoi();
            }

            if (chiHienThiVatPhamNoiBat)
            {
                danhSach = itemDatabase.LayVatPhamNoiBat();
            }

            return danhSach;
        }

        /// <summary>
        /// Lấy danh sách vật phẩm theo loại
        /// </summary>
        /// <param name="loai">Loại vật phẩm</param>
        /// <returns>Danh sách vật phẩm</returns>
        public List<Item> LayDanhSachVatPhamTheoLoai(ItemType loai)
        {
            if (itemDatabase == null)
                return new List<Item>();

            return itemDatabase.LayVatPhamTheoLoai(loai);
        }

        /// <summary>
        /// Tìm kiếm vật phẩm
        /// </summary>
        /// <param name="tuKhoa">Từ khóa tìm kiếm</param>
        /// <returns>Danh sách vật phẩm</returns>
        public List<Item> TimKiemVatPham(string tuKhoa)
        {
            if (itemDatabase == null)
                return new List<Item>();

            return itemDatabase.TimKiemVatPham(tuKhoa);
        }

        /// <summary>
        /// Đặt bộ lọc tìm kiếm
        /// </summary>
        /// <param name="tuKhoa">Từ khóa</param>
        public void DatBoLocTimKiem(string tuKhoa)
        {
            tuKhoaTimKiem = tuKhoa;
            OnShopUpdated?.Invoke();
        }

        /// <summary>
        /// Đặt bộ lọc loại vật phẩm
        /// </summary>
        /// <param name="loai">Loại vật phẩm</param>
        public void DatBoLocLoai(ItemType loai)
        {
            boLocLoai = loai;
            OnShopUpdated?.Invoke();
        }

        /// <summary>
        /// Bật/tắt hiển thị vật phẩm mới
        /// </summary>
        /// <param name="hienThi">True để hiển thị</param>
        public void DatHienThiVatPhamMoi(bool hienThi)
        {
            chiHienThiVatPhamMoi = hienThi;
            OnShopUpdated?.Invoke();
        }

        /// <summary>
        /// Bật/tắt hiển thị vật phẩm nổi bật
        /// </summary>
        /// <param name="hienThi">True để hiển thị</param>
        public void DatHienThiVatPhamNoiBat(bool hienThi)
        {
            chiHienThiVatPhamNoiBat = hienThi;
            OnShopUpdated?.Invoke();
        }
        #endregion

        #region Editor Methods
        #if UNITY_EDITOR
        [ContextMenu("Test Mua Vật Phẩm")]
        private void TestMuaVatPham()
        {
            if (itemDatabase != null)
            {
                var danhSach = itemDatabase.LayDanhSachVatPhamBan();
                if (danhSach.Count > 0)
                {
                    MuaVatPham(danhSach[0], 1);
                }
            }
        }

        [ContextMenu("Test Bán Vật Phẩm")]
        private void TestBanVatPham()
        {
            var inventory = InventoryManager.Instance.DanhSachVatPham;
            if (inventory.Count > 0)
            {
                BanVatPham(inventory[0].Item, 1);
            }
        }
        #endif
        #endregion
    }
}
