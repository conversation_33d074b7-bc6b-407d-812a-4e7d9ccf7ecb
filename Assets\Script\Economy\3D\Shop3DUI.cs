using UnityEngine;
using UnityEngine.UI;
using TMPro;
using System.Collections;

namespace EconomySystem.Shop3D
{
    /// <summary>
    /// UI Manager cho cửa hàng 3D
    /// Hiển thị thông tin vật phẩm và xử lý giao dịch
    /// </summary>
    public class Shop3DUI : MonoBehaviour
    {
        #region Singleton
        private static Shop3DUI _instance;
        public static Shop3DUI Instance
        {
            get
            {
                if (_instance == null)
                {
                    _instance = FindObjectOfType<Shop3DUI>();
                }
                return _instance;
            }
        }
        #endregion

        [Header("Main UI Panels")]
        [SerializeField] private GameObject shopMenuPanel;
        [SerializeField] private GameObject itemDetailsPanel;
        [SerializeField] private GameObject confirmationPanel;
        [SerializeField] private GameObject currencyHUD;

        [Header("Item Details UI")]
        [SerializeField] private Image itemIcon;
        [SerializeField] private TextMeshProUGUI itemNameText;
        [SerializeField] private TextMeshProUGUI itemDescriptionText;
        [SerializeField] private TextMeshProUG<PERSON> itemPriceText;
        [SerializeField] private TextMeshP<PERSON>U<PERSON><PERSON> itemTypeText;
        [SerializeField] private TextMeshProUGUI itemRarityText;
        [SerializeField] private TextMeshProUGUI stockQuantityText;

        [Header("Transaction UI")]
        [SerializeField] private Slider quantitySlider;
        [SerializeField] private TMP_InputField quantityInput;
        [SerializeField] private TextMeshProUGUI totalPriceText;
        [SerializeField] private Button buyButton;
        [SerializeField] private Button sellButton;
        [SerializeField] private Button closeButton;

        [Header("Currency HUD")]
        [SerializeField] private TextMeshProUGUI playerMoneyText;
        [SerializeField] private Image currencyIcon;
        [SerializeField] private GameObject moneyChangeEffect;

        [Header("Confirmation Dialog")]
        [SerializeField] private TextMeshProUGUI confirmationText;
        [SerializeField] private Button confirmButton;
        [SerializeField] private Button cancelButton;

        [Header("Animation Settings")]
        [SerializeField] private float panelAnimationDuration = 0.3f;
        [SerializeField] private AnimationCurve panelAnimationCurve = AnimationCurve.EaseInOut(0, 0, 1, 1);

        [Header("Audio")]
        [SerializeField] private AudioClip openUISound;
        [SerializeField] private AudioClip closeUISound;
        [SerializeField] private AudioClip purchaseSound;
        [SerializeField] private AudioClip errorSound;
        [SerializeField] private AudioSource audioSource;

        // Private variables
        private InteractableItem3D currentItem;
        private int selectedQuantity = 1;
        private bool isShopMenuOpen = false;
        private bool isItemDetailsOpen = false;
        private Coroutine currentAnimation;

        #region Unity Methods
        private void Awake()
        {
            if (_instance == null)
            {
                _instance = this;
            }
            else if (_instance != this)
            {
                Destroy(gameObject);
                return;
            }

            InitializeComponents();
        }

        private void Start()
        {
            SetupUI();
            RegisterEvents();
        }

        private void Update()
        {
            UpdateCurrencyHUD();
            HandleInput();
        }

        private void OnDestroy()
        {
            UnregisterEvents();
        }
        #endregion

        #region Initialization
        private void InitializeComponents()
        {
            // Tạo AudioSource nếu chưa có
            if (audioSource == null)
            {
                audioSource = GetComponent<AudioSource>();
                if (audioSource == null)
                {
                    audioSource = gameObject.AddComponent<AudioSource>();
                    audioSource.playOnAwake = false;
                }
            }
        }

        private void SetupUI()
        {
            // Ẩn tất cả panels ban đầu
            if (shopMenuPanel != null) shopMenuPanel.SetActive(false);
            if (itemDetailsPanel != null) itemDetailsPanel.SetActive(false);
            if (confirmationPanel != null) confirmationPanel.SetActive(false);

            // Hiển thị currency HUD
            if (currencyHUD != null) currencyHUD.SetActive(true);

            // Setup buttons
            SetupButtons();

            // Setup sliders
            SetupSliders();
        }

        private void SetupButtons()
        {
            if (buyButton != null)
                buyButton.onClick.AddListener(() => ShowConfirmation(true));

            if (sellButton != null)
                sellButton.onClick.AddListener(() => ShowConfirmation(false));

            if (closeButton != null)
                closeButton.onClick.AddListener(CloseItemDetails);

            if (confirmButton != null)
                confirmButton.onClick.AddListener(ConfirmTransaction);

            if (cancelButton != null)
                cancelButton.onClick.AddListener(CloseConfirmation);
        }

        private void SetupSliders()
        {
            if (quantitySlider != null)
            {
                quantitySlider.onValueChanged.AddListener(OnQuantitySliderChanged);
                quantitySlider.minValue = 1;
                quantitySlider.maxValue = 10;
                quantitySlider.value = 1;
            }

            if (quantityInput != null)
            {
                quantityInput.onValueChanged.AddListener(OnQuantityInputChanged);
            }
        }

        private void RegisterEvents()
        {
            // Đăng ký events từ Economy System
            if (CurrencyManager.Instance != null)
            {
                CurrencyManager.OnCurrencyChanged += OnCurrencyChanged;
                CurrencyManager.OnTransactionCompleted += OnTransactionCompleted;
            }

            if (ShopManager.Instance != null)
            {
                ShopManager.OnItemPurchased += OnItemPurchased;
                ShopManager.OnItemSold += OnItemSold;
                ShopManager.OnTransactionFailed += OnTransactionFailed;
            }
        }

        private void UnregisterEvents()
        {
            if (CurrencyManager.Instance != null)
            {
                CurrencyManager.OnCurrencyChanged -= OnCurrencyChanged;
                CurrencyManager.OnTransactionCompleted -= OnTransactionCompleted;
            }

            if (ShopManager.Instance != null)
            {
                ShopManager.OnItemPurchased -= OnItemPurchased;
                ShopManager.OnItemSold -= OnItemSold;
                ShopManager.OnTransactionFailed -= OnTransactionFailed;
            }
        }
        #endregion

        #region Public Methods
        public void ShowItemDetails(InteractableItem3D item)
        {
            if (item == null || item.ItemData == null) return;

            currentItem = item;
            UpdateItemDetailsUI();
            ShowPanel(itemDetailsPanel);
            isItemDetailsOpen = true;

            PlaySound(openUISound);

            // Pause game hoặc lock cursor nếu cần
            // Time.timeScale = 0f;
            // Cursor.lockState = CursorLockMode.None;
        }

        public void CloseItemDetails()
        {
            HidePanel(itemDetailsPanel);
            isItemDetailsOpen = false;
            currentItem = null;

            PlaySound(closeUISound);

            // Resume game
            // Time.timeScale = 1f;
            // Cursor.lockState = CursorLockMode.Locked;
        }

        public void ToggleShopMenu()
        {
            if (isShopMenuOpen)
            {
                CloseShopMenu();
            }
            else
            {
                OpenShopMenu();
            }
        }

        public void OpenShopMenu()
        {
            if (shopMenuPanel != null)
            {
                ShowPanel(shopMenuPanel);
                isShopMenuOpen = true;
                PlaySound(openUISound);
            }
        }

        public void CloseShopMenu()
        {
            if (shopMenuPanel != null)
            {
                HidePanel(shopMenuPanel);
                isShopMenuOpen = false;
                PlaySound(closeUISound);
            }
        }
        #endregion

        #region UI Updates
        private void UpdateItemDetailsUI()
        {
            if (currentItem?.ItemData == null) return;

            var item = currentItem.ItemData;

            // Update item info
            if (itemNameText != null)
                itemNameText.text = item.TenVatPham;

            if (itemDescriptionText != null)
                itemDescriptionText.text = item.MoTa;

            if (itemPriceText != null)
                itemPriceText.text = $"{item.GiaMua:N0} Lea";

            if (itemTypeText != null)
                itemTypeText.text = item.LayTenLoaiVatPham();

            if (itemRarityText != null)
            {
                itemRarityText.text = item.LayTenDoHiem();
                itemRarityText.color = item.LayMauTheoDoHiem();
            }

            if (stockQuantityText != null)
                stockQuantityText.text = $"Còn lại: {currentItem.StockQuantity}";

            if (itemIcon != null && item.Icon != null)
                itemIcon.sprite = item.Icon;

            // Update quantity controls
            if (quantitySlider != null)
            {
                quantitySlider.maxValue = Mathf.Min(10, currentItem.StockQuantity);
                quantitySlider.value = 1;
            }

            // Update buttons
            UpdateTransactionButtons();

            // Update total price
            UpdateTotalPrice();
        }

        private void UpdateTransactionButtons()
        {
            if (currentItem == null) return;

            // Buy button
            if (buyButton != null)
            {
                buyButton.interactable = currentItem.CanPurchase(selectedQuantity);
            }

            // Sell button
            if (sellButton != null)
            {
                sellButton.interactable = currentItem.CanSell(selectedQuantity);
            }
        }

        private void UpdateTotalPrice()
        {
            if (currentItem?.ItemData == null || totalPriceText == null) return;

            int totalPrice = currentItem.ItemData.GiaMua * selectedQuantity;
            totalPriceText.text = $"Tổng: {totalPrice:N0} Lea";
        }

        private void UpdateCurrencyHUD()
        {
            if (playerMoneyText != null && CurrencyManager.Instance != null)
            {
                playerMoneyText.text = $"{CurrencyManager.Instance.SoTienHienTai:N0} Lea";
            }
        }
        #endregion

        #region Event Handlers
        private void OnQuantitySliderChanged(float value)
        {
            selectedQuantity = Mathf.RoundToInt(value);
            
            if (quantityInput != null)
                quantityInput.text = selectedQuantity.ToString();

            UpdateTotalPrice();
            UpdateTransactionButtons();
        }

        private void OnQuantityInputChanged(string value)
        {
            if (int.TryParse(value, out int quantity))
            {
                selectedQuantity = Mathf.Clamp(quantity, 1, (int)quantitySlider.maxValue);
                
                if (quantitySlider != null)
                    quantitySlider.value = selectedQuantity;

                UpdateTotalPrice();
                UpdateTransactionButtons();
            }
        }

        private void OnCurrencyChanged(int newAmount)
        {
            // Hiệu ứng thay đổi tiền
            if (moneyChangeEffect != null)
            {
                StartCoroutine(PlayMoneyChangeEffect());
            }
        }

        private void OnTransactionCompleted(int amountChanged, string reason)
        {
            // Có thể hiển thị notification ở đây
        }

        private void OnItemPurchased(Item item, int quantity)
        {
            PlaySound(purchaseSound);
            ShowTransactionFeedback($"Đã mua {quantity}x {item.TenVatPham}!", Color.green);
        }

        private void OnItemSold(Item item, int quantity)
        {
            PlaySound(purchaseSound);
            ShowTransactionFeedback($"Đã bán {quantity}x {item.TenVatPham}!", Color.blue);
        }

        private void OnTransactionFailed(string reason)
        {
            PlaySound(errorSound);
            ShowTransactionFeedback(reason, Color.red);
        }
        #endregion

        #region Transaction Logic
        private void ShowConfirmation(bool isBuying)
        {
            if (currentItem?.ItemData == null) return;

            string action = isBuying ? "mua" : "bán";
            string itemName = currentItem.ItemData.TenVatPham;
            int totalPrice = isBuying ? 
                currentItem.ItemData.GiaMua * selectedQuantity :
                currentItem.ItemData.GiaBan * selectedQuantity;

            if (confirmationText != null)
            {
                confirmationText.text = $"Bạn có chắc muốn {action} {selectedQuantity}x {itemName} " +
                                       $"với giá {totalPrice:N0} Lea?";
            }

            // Store transaction type
            confirmButton.onClick.RemoveAllListeners();
            confirmButton.onClick.AddListener(() => {
                if (isBuying)
                    ExecutePurchase();
                else
                    ExecuteSale();
            });

            ShowPanel(confirmationPanel);
        }

        private void CloseConfirmation()
        {
            HidePanel(confirmationPanel);
        }

        private void ConfirmTransaction()
        {
            // This will be set by ShowConfirmation
        }

        private void ExecutePurchase()
        {
            if (currentItem != null)
            {
                bool success = currentItem.PurchaseItem(selectedQuantity);
                if (success)
                {
                    UpdateItemDetailsUI(); // Refresh UI
                }
            }
            CloseConfirmation();
        }

        private void ExecuteSale()
        {
            if (currentItem != null)
            {
                bool success = currentItem.SellItem(selectedQuantity);
                if (success)
                {
                    UpdateItemDetailsUI(); // Refresh UI
                }
            }
            CloseConfirmation();
        }
        #endregion

        #region Input Handling
        private void HandleInput()
        {
            // ESC để đóng UI
            if (Input.GetKeyDown(KeyCode.Escape))
            {
                if (confirmationPanel != null && confirmationPanel.activeSelf)
                {
                    CloseConfirmation();
                }
                else if (isItemDetailsOpen)
                {
                    CloseItemDetails();
                }
                else if (isShopMenuOpen)
                {
                    CloseShopMenu();
                }
            }
        }
        #endregion

        #region Animation and Effects
        private void ShowPanel(GameObject panel)
        {
            if (panel == null) return;

            if (currentAnimation != null)
                StopCoroutine(currentAnimation);

            panel.SetActive(true);
            currentAnimation = StartCoroutine(AnimatePanel(panel, true));
        }

        private void HidePanel(GameObject panel)
        {
            if (panel == null) return;

            if (currentAnimation != null)
                StopCoroutine(currentAnimation);

            currentAnimation = StartCoroutine(AnimatePanel(panel, false));
        }

        private IEnumerator AnimatePanel(GameObject panel, bool show)
        {
            CanvasGroup canvasGroup = panel.GetComponent<CanvasGroup>();
            if (canvasGroup == null)
                canvasGroup = panel.AddComponent<CanvasGroup>();

            float startAlpha = show ? 0f : 1f;
            float endAlpha = show ? 1f : 0f;
            float startScale = show ? 0.8f : 1f;
            float endScale = show ? 1f : 0.8f;

            Vector3 originalScale = panel.transform.localScale;
            
            float elapsedTime = 0f;
            while (elapsedTime < panelAnimationDuration)
            {
                elapsedTime += Time.unscaledDeltaTime;
                float t = elapsedTime / panelAnimationDuration;
                float curveValue = panelAnimationCurve.Evaluate(t);

                canvasGroup.alpha = Mathf.Lerp(startAlpha, endAlpha, curveValue);
                float scale = Mathf.Lerp(startScale, endScale, curveValue);
                panel.transform.localScale = originalScale * scale;

                yield return null;
            }

            canvasGroup.alpha = endAlpha;
            panel.transform.localScale = originalScale * endScale;

            if (!show)
            {
                panel.SetActive(false);
            }

            currentAnimation = null;
        }

        private IEnumerator PlayMoneyChangeEffect()
        {
            if (moneyChangeEffect == null) yield break;

            moneyChangeEffect.SetActive(true);
            yield return new WaitForSeconds(1f);
            moneyChangeEffect.SetActive(false);
        }

        private void ShowTransactionFeedback(string message, Color color)
        {
            // Có thể tạo floating text hoặc notification popup
            Debug.Log($"[Shop3D] {message}");
        }
        #endregion

        #region Audio
        private void PlaySound(AudioClip clip)
        {
            if (audioSource != null && clip != null)
            {
                audioSource.PlayOneShot(clip);
            }
        }
        #endregion

        #region Properties
        public bool IsShopMenuOpen => isShopMenuOpen;
        public bool IsItemDetailsOpen => isItemDetailsOpen;
        public InteractableItem3D CurrentItem => currentItem;
        #endregion
    }
}
