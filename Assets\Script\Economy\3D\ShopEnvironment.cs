using UnityEngine;
using System.Collections.Generic;
using System.Collections;

namespace EconomySystem.Shop3D
{
    /// <summary>
    /// Quản lý môi trường cửa hàng 3D
    /// Tạo và sắp xếp các vật phẩm trên kệ, lighting, atmosphere
    /// </summary>
    public class ShopEnvironment : MonoBehaviour
    {
        [Header("Shop Layout")]
        [SerializeField] private Transform[] shelfPositions;
        [SerializeField] private Transform cashierCounter;
        [SerializeField] private Transform playerSpawnPoint;
        [SerializeField] private Transform shopEntrance;
        [SerializeField] private Transform shopExit;

        [Header("Item Spawning")]
        [SerializeField] private GameObject itemDisplayPrefab;
        [SerializeField] private float itemSpacing = 1f;
        [SerializeField] private int maxItemsPerShelf = 5;
        [SerializeField] private bool autoArrangeItems = true;
        [SerializeField] private bool randomizeItemPositions = false;

        [Header("Lighting")]
        [SerializeField] private Light[] shopLights;
        [SerializeField] private Color warmLightColor = new Color(1f, 0.9f, 0.7f);
        [SerializeField] private float lightIntensity = 1.2f;
        [SerializeField] private bool enableDynamicLighting = true;
        [SerializeField] private AnimationCurve lightFlickerCurve;

        [Header("Atmosphere")]
        [SerializeField] private AudioSource backgroundMusicSource;
        [SerializeField] private AudioClip[] backgroundMusicTracks;
        [SerializeField] private AudioSource ambientSoundSource;
        [SerializeField] private AudioClip[] ambientSounds;
        [SerializeField] private ParticleSystem[] atmosphereParticles;

        [Header("Interactive Elements")]
        [SerializeField] private GameObject[] decorativeObjects;
        [SerializeField] private Animator[] animatedElements;
        [SerializeField] private bool enableRandomAnimations = true;

        [Header("Shop Signs")]
        [SerializeField] private TextMesh shopNameText;
        [SerializeField] private string shopName = "Cửa Hàng Lea";
        [SerializeField] private TextMesh[] categorySignTexts;
        [SerializeField] private string[] categoryNames = {"Vũ Khí", "Giáp", "Vật Liệu", "Tiêu Hao"};

        // Private variables
        private List<InteractableItem3D> spawnedItems = new List<InteractableItem3D>();
        private Coroutine lightingCoroutine;
        private Coroutine musicCoroutine;
        private int currentMusicTrack = 0;

        #region Unity Methods
        private void Awake()
        {
            ValidateReferences();
        }

        private void Start()
        {
            InitializeShopEnvironment();
        }

        private void OnDestroy()
        {
            StopAllCoroutines();
        }
        #endregion

        #region Initialization
        private void ValidateReferences()
        {
            // Tạo spawn point nếu chưa có
            if (playerSpawnPoint == null)
            {
                GameObject spawnObj = new GameObject("PlayerSpawnPoint");
                spawnObj.transform.SetParent(transform);
                spawnObj.transform.localPosition = Vector3.zero;
                playerSpawnPoint = spawnObj.transform;
            }

            // Tìm shelf positions nếu chưa gán
            if (shelfPositions == null || shelfPositions.Length == 0)
            {
                FindShelfPositions();
            }
        }

        private void FindShelfPositions()
        {
            // Tự động tìm các transform có tag "Shelf"
            GameObject[] shelves = GameObject.FindGameObjectsWithTag("Shelf");
            shelfPositions = new Transform[shelves.Length];
            
            for (int i = 0; i < shelves.Length; i++)
            {
                shelfPositions[i] = shelves[i].transform;
            }

            if (shelfPositions.Length == 0)
            {
                Debug.LogWarning("Không tìm thấy shelf positions. Hãy gán thủ công hoặc tag các GameObject với 'Shelf'");
            }
        }

        private void InitializeShopEnvironment()
        {
            SetupLighting();
            SetupAudio();
            SetupSigns();
            SpawnShopItems();
            SetupAtmosphere();
            
            if (enableRandomAnimations)
            {
                StartRandomAnimations();
            }
        }
        #endregion

        #region Item Spawning
        public void SpawnShopItems()
        {
            if (ShopManager.Instance?.ItemDatabase == null)
            {
                Debug.LogWarning("ShopManager hoặc ItemDatabase chưa được khởi tạo!");
                return;
            }

            ClearExistingItems();

            var availableItems = ShopManager.Instance.ItemDatabase.LayDanhSachVatPhamBan();
            
            if (availableItems.Count == 0)
            {
                Debug.LogWarning("Không có vật phẩm nào trong database!");
                return;
            }

            SpawnItemsOnShelves(availableItems);
        }

        private void SpawnItemsOnShelves(List<Item> items)
        {
            int itemIndex = 0;
            
            foreach (var shelf in shelfPositions)
            {
                if (shelf == null) continue;

                int itemsOnThisShelf = Mathf.Min(maxItemsPerShelf, items.Count - itemIndex);
                
                for (int i = 0; i < itemsOnThisShelf; i++)
                {
                    if (itemIndex >= items.Count) break;

                    Vector3 spawnPosition = CalculateItemPosition(shelf, i, itemsOnThisShelf);
                    SpawnSingleItem(items[itemIndex], spawnPosition, shelf);
                    
                    itemIndex++;
                }

                if (itemIndex >= items.Count) break;
            }

            Debug.Log($"Đã spawn {spawnedItems.Count} vật phẩm trong cửa hàng");
        }

        private Vector3 CalculateItemPosition(Transform shelf, int itemIndex, int totalItems)
        {
            Vector3 basePosition = shelf.position;
            
            if (randomizeItemPositions)
            {
                // Random position trong phạm vi shelf
                Vector3 randomOffset = new Vector3(
                    Random.Range(-itemSpacing, itemSpacing),
                    Random.Range(0, 0.5f),
                    Random.Range(-itemSpacing, itemSpacing)
                );
                return basePosition + randomOffset;
            }
            else
            {
                // Sắp xếp đều trên shelf
                float startX = -(totalItems - 1) * itemSpacing * 0.5f;
                Vector3 offset = new Vector3(startX + itemIndex * itemSpacing, 0.5f, 0);
                return basePosition + shelf.TransformDirection(offset);
            }
        }

        private void SpawnSingleItem(Item item, Vector3 position, Transform parent)
        {
            if (itemDisplayPrefab == null)
            {
                Debug.LogError("Item Display Prefab chưa được gán!");
                return;
            }

            GameObject itemObj = Instantiate(itemDisplayPrefab, position, Quaternion.identity, parent);
            itemObj.name = $"Item_{item.ID}_{item.TenVatPham}";

            // Setup InteractableItem3D component
            InteractableItem3D interactable = itemObj.GetComponent<InteractableItem3D>();
            if (interactable == null)
            {
                interactable = itemObj.AddComponent<InteractableItem3D>();
            }

            // Configure item
            SetupInteractableItem(interactable, item);

            spawnedItems.Add(interactable);
        }

        private void SetupInteractableItem(InteractableItem3D interactable, Item item)
        {
            // Sử dụng reflection để set private fields
            var itemField = typeof(InteractableItem3D).GetField("itemData", 
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            itemField?.SetValue(interactable, item);

            var idField = typeof(InteractableItem3D).GetField("itemID", 
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            idField?.SetValue(interactable, item.ID);

            // Set random stock quantity
            var stockField = typeof(InteractableItem3D).GetField("stockQuantity", 
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            stockField?.SetValue(interactable, Random.Range(5, 15));
        }

        private void ClearExistingItems()
        {
            foreach (var item in spawnedItems)
            {
                if (item != null)
                {
                    DestroyImmediate(item.gameObject);
                }
            }
            spawnedItems.Clear();
        }
        #endregion

        #region Lighting
        private void SetupLighting()
        {
            // Configure shop lights
            foreach (var light in shopLights)
            {
                if (light != null)
                {
                    light.color = warmLightColor;
                    light.intensity = lightIntensity;
                    light.type = LightType.Point;
                    light.range = 10f;
                }
            }

            // Start dynamic lighting if enabled
            if (enableDynamicLighting)
            {
                lightingCoroutine = StartCoroutine(DynamicLightingCoroutine());
            }
        }

        private IEnumerator DynamicLightingCoroutine()
        {
            while (true)
            {
                foreach (var light in shopLights)
                {
                    if (light != null)
                    {
                        // Subtle flickering effect
                        float flicker = lightFlickerCurve != null ? 
                            lightFlickerCurve.Evaluate(Time.time % 1f) : 
                            1f + Mathf.Sin(Time.time * 2f) * 0.05f;
                        
                        light.intensity = lightIntensity * flicker;
                    }
                }
                
                yield return new WaitForSeconds(0.1f);
            }
        }
        #endregion

        #region Audio
        private void SetupAudio()
        {
            SetupBackgroundMusic();
            SetupAmbientSounds();
        }

        private void SetupBackgroundMusic()
        {
            if (backgroundMusicSource != null && backgroundMusicTracks.Length > 0)
            {
                backgroundMusicSource.loop = false;
                backgroundMusicSource.volume = 0.3f;
                musicCoroutine = StartCoroutine(BackgroundMusicCoroutine());
            }
        }

        private void SetupAmbientSounds()
        {
            if (ambientSoundSource != null && ambientSounds.Length > 0)
            {
                ambientSoundSource.loop = true;
                ambientSoundSource.volume = 0.2f;
                
                // Play random ambient sound
                AudioClip randomAmbient = ambientSounds[Random.Range(0, ambientSounds.Length)];
                ambientSoundSource.clip = randomAmbient;
                ambientSoundSource.Play();
            }
        }

        private IEnumerator BackgroundMusicCoroutine()
        {
            while (true)
            {
                if (backgroundMusicTracks.Length > 0)
                {
                    AudioClip currentTrack = backgroundMusicTracks[currentMusicTrack];
                    backgroundMusicSource.clip = currentTrack;
                    backgroundMusicSource.Play();

                    // Wait for track to finish
                    yield return new WaitForSeconds(currentTrack.length);

                    // Move to next track
                    currentMusicTrack = (currentMusicTrack + 1) % backgroundMusicTracks.Length;
                }
                else
                {
                    yield return new WaitForSeconds(1f);
                }
            }
        }
        #endregion

        #region Signs and UI
        private void SetupSigns()
        {
            // Setup shop name
            if (shopNameText != null)
            {
                shopNameText.text = shopName;
                shopNameText.fontSize = 20;
                shopNameText.color = Color.white;
            }

            // Setup category signs
            for (int i = 0; i < categorySignTexts.Length && i < categoryNames.Length; i++)
            {
                if (categorySignTexts[i] != null)
                {
                    categorySignTexts[i].text = categoryNames[i];
                    categorySignTexts[i].fontSize = 12;
                    categorySignTexts[i].color = Color.yellow;
                }
            }
        }
        #endregion

        #region Atmosphere
        private void SetupAtmosphere()
        {
            // Start particle effects
            foreach (var particles in atmosphereParticles)
            {
                if (particles != null)
                {
                    particles.Play();
                }
            }
        }

        private void StartRandomAnimations()
        {
            StartCoroutine(RandomAnimationCoroutine());
        }

        private IEnumerator RandomAnimationCoroutine()
        {
            while (true)
            {
                yield return new WaitForSeconds(Random.Range(5f, 15f));

                // Trigger random animation
                if (animatedElements.Length > 0)
                {
                    Animator randomAnimator = animatedElements[Random.Range(0, animatedElements.Length)];
                    if (randomAnimator != null)
                    {
                        randomAnimator.SetTrigger("RandomAnimation");
                    }
                }
            }
        }
        #endregion

        #region Public Methods
        public void RefreshShopItems()
        {
            SpawnShopItems();
        }

        public void SetShopName(string newName)
        {
            shopName = newName;
            if (shopNameText != null)
            {
                shopNameText.text = shopName;
            }
        }

        public void TeleportPlayerToSpawn()
        {
            var player = FindObjectOfType<PlayerController3D>();
            if (player != null && playerSpawnPoint != null)
            {
                player.Teleport(playerSpawnPoint.position);
                player.SetLookDirection(playerSpawnPoint.forward);
            }
        }

        public Vector3 GetPlayerSpawnPosition()
        {
            return playerSpawnPoint != null ? playerSpawnPoint.position : Vector3.zero;
        }

        public List<InteractableItem3D> GetAllShopItems()
        {
            return new List<InteractableItem3D>(spawnedItems);
        }
        #endregion

        #region Editor Methods
        #if UNITY_EDITOR
        [ContextMenu("Refresh Shop Items")]
        private void EditorRefreshShopItems()
        {
            SpawnShopItems();
        }

        [ContextMenu("Clear All Items")]
        private void EditorClearAllItems()
        {
            ClearExistingItems();
        }

        [ContextMenu("Setup Test Environment")]
        private void EditorSetupTestEnvironment()
        {
            // Tạo test shelves nếu chưa có
            if (shelfPositions.Length == 0)
            {
                CreateTestShelves();
            }
        }

        private void CreateTestShelves()
        {
            List<Transform> shelves = new List<Transform>();
            
            for (int i = 0; i < 4; i++)
            {
                GameObject shelf = new GameObject($"TestShelf_{i}");
                shelf.transform.SetParent(transform);
                shelf.transform.localPosition = new Vector3(i * 3f - 4.5f, 0, 0);
                shelf.tag = "Shelf";
                
                // Tạo visual representation
                GameObject cube = GameObject.CreatePrimitive(PrimitiveType.Cube);
                cube.transform.SetParent(shelf.transform);
                cube.transform.localPosition = Vector3.zero;
                cube.transform.localScale = new Vector3(2f, 1f, 0.5f);
                
                shelves.Add(shelf.transform);
            }
            
            shelfPositions = shelves.ToArray();
        }
        #endif
        #endregion
    }
}
