using System;
using System.Collections.Generic;
using System.Linq;
using UnityEngine;

namespace EconomySystem
{
    /// <summary>
    /// Manager quản lý inventory của người chơi
    /// Tích hợp với hệ thống tiền tệ và shop
    /// </summary>
    public class InventoryManager : MonoBehaviour
    {
        #region Singleton
        private static InventoryManager _instance;
        public static InventoryManager Instance
        {
            get
            {
                if (_instance == null)
                {
                    _instance = FindObjectOfType<InventoryManager>();
                    if (_instance == null)
                    {
                        GameObject go = new GameObject("InventoryManager");
                        _instance = go.AddComponent<InventoryManager>();
                        DontDestroyOnLoad(go);
                    }
                }
                return _instance;
            }
        }
        #endregion

        #region Events
        /// <summary>
        /// Event khi thêm vật phẩm (item, số lượng)
        /// </summary>
        public static event Action<Item, int> OnItemAdded;

        /// <summary>
        /// Event khi xóa vật phẩm (item, số lượng)
        /// </summary>
        public static event Action<Item, int> OnItemRemoved;

        /// <summary>
        /// Event khi inventory thay đổi
        /// </summary>
        public static event Action OnInventoryChanged;

        /// <summary>
        /// Event khi inventory đầy
        /// </summary>
        public static event Action OnInventoryFull;
        #endregion

        #region Fields
        [Header("Cài Đặt Inventory")]
        [SerializeField] private int soSlotToiDa = 50;
        [SerializeField] private bool tuDongSapXep = true;
        [SerializeField] private bool tuDongLuu = true;
        [SerializeField] private bool hienThiLog = true;

        [Header("Dữ Liệu Inventory")]
        [SerializeField] private List<InventoryItem> danhSachVatPham = new List<InventoryItem>();

        private const string INVENTORY_SAVE_KEY = "PlayerInventory";
        #endregion

        #region Properties
        /// <summary>
        /// Danh sách vật phẩm trong inventory
        /// </summary>
        public List<InventoryItem> DanhSachVatPham => danhSachVatPham;

        /// <summary>
        /// Số slot đã sử dụng
        /// </summary>
        public int SoSlotDaSuDung => danhSachVatPham.Count;

        /// <summary>
        /// Số slot trống
        /// </summary>
        public int SoSlotTrong => soSlotToiDa - SoSlotDaSuDung;

        /// <summary>
        /// Kiểm tra inventory có đầy không
        /// </summary>
        public bool InventoryDay => SoSlotTrong <= 0;

        /// <summary>
        /// Tổng số vật phẩm (tính cả số lượng)
        /// </summary>
        public int TongSoVatPham => danhSachVatPham.Sum(x => x.SoLuong);
        #endregion

        #region Unity Methods
        private void Awake()
        {
            if (_instance == null)
            {
                _instance = this;
                DontDestroyOnLoad(gameObject);
                TaiDuLieu();
            }
            else if (_instance != this)
            {
                Destroy(gameObject);
            }
        }

        private void Start()
        {
            OnInventoryChanged?.Invoke();
        }

        private void OnApplicationPause(bool pauseStatus)
        {
            if (pauseStatus && tuDongLuu)
                LuuDuLieu();
        }

        private void OnApplicationFocus(bool hasFocus)
        {
            if (!hasFocus && tuDongLuu)
                LuuDuLieu();
        }

        private void OnDestroy()
        {
            if (tuDongLuu)
                LuuDuLieu();
        }
        #endregion

        #region Public Methods
        /// <summary>
        /// Thêm vật phẩm vào inventory
        /// </summary>
        /// <param name="item">Vật phẩm cần thêm</param>
        /// <param name="soLuong">Số lượng</param>
        /// <returns>Số lượng thực tế đã thêm</returns>
        public int ThemVatPham(Item item, int soLuong)
        {
            if (item == null || soLuong <= 0)
                return 0;

            int soLuongDaThemVao = 0;
            int soLuongConLai = soLuong;

            // Tìm vật phẩm đã có trong inventory
            var vatPhamTonTai = danhSachVatPham.FirstOrDefault(x => x.Item.ID == item.ID);

            if (vatPhamTonTai != null)
            {
                // Thêm vào stack hiện có
                int soLuongCoTheThemVaoStack = vatPhamTonTai.Item.SoLuongToiDa - vatPhamTonTai.SoLuong;
                int soLuongThemVaoStack = Mathf.Min(soLuongConLai, soLuongCoTheThemVaoStack);

                if (soLuongThemVaoStack > 0)
                {
                    vatPhamTonTai.ThemSoLuong(soLuongThemVaoStack);
                    soLuongDaThemVao += soLuongThemVaoStack;
                    soLuongConLai -= soLuongThemVaoStack;
                }
            }

            // Tạo stack mới nếu còn vật phẩm và có slot trống
            while (soLuongConLai > 0 && !InventoryDay)
            {
                int soLuongChoStackMoi = Mathf.Min(soLuongConLai, item.SoLuongToiDa);
                danhSachVatPham.Add(new InventoryItem(item, soLuongChoStackMoi));
                soLuongDaThemVao += soLuongChoStackMoi;
                soLuongConLai -= soLuongChoStackMoi;
            }

            if (soLuongDaThemVao > 0)
            {
                if (tuDongSapXep)
                    SapXepInventory();

                if (hienThiLog)
                    Debug.Log($"Đã thêm {soLuongDaThemVao}x {item.TenVatPham} vào inventory");

                OnItemAdded?.Invoke(item, soLuongDaThemVao);
                OnInventoryChanged?.Invoke();

                if (tuDongLuu)
                    LuuDuLieu();
            }

            if (soLuongConLai > 0)
            {
                if (hienThiLog)
                    Debug.LogWarning($"Inventory đầy! Không thể thêm {soLuongConLai}x {item.TenVatPham}");
                
                OnInventoryFull?.Invoke();
            }

            return soLuongDaThemVao;
        }

        /// <summary>
        /// Xóa vật phẩm khỏi inventory
        /// </summary>
        /// <param name="itemID">ID vật phẩm</param>
        /// <param name="soLuong">Số lượng cần xóa</param>
        /// <returns>Số lượng thực tế đã xóa</returns>
        public int XoaVatPham(string itemID, int soLuong)
        {
            if (string.IsNullOrEmpty(itemID) || soLuong <= 0)
                return 0;

            int soLuongDaXoa = 0;
            int soLuongCanXoa = soLuong;

            // Xóa từ các stack, bắt đầu từ cuối danh sách
            for (int i = danhSachVatPham.Count - 1; i >= 0 && soLuongCanXoa > 0; i--)
            {
                var inventoryItem = danhSachVatPham[i];
                if (inventoryItem.Item.ID == itemID)
                {
                    int soLuongXoaTuStack = Mathf.Min(soLuongCanXoa, inventoryItem.SoLuong);
                    inventoryItem.TruSoLuong(soLuongXoaTuStack);
                    soLuongDaXoa += soLuongXoaTuStack;
                    soLuongCanXoa -= soLuongXoaTuStack;

                    // Xóa stack nếu hết vật phẩm
                    if (inventoryItem.SoLuong <= 0)
                    {
                        danhSachVatPham.RemoveAt(i);
                    }
                }
            }

            if (soLuongDaXoa > 0)
            {
                var item = GetItemByID(itemID);
                if (item != null)
                {
                    if (hienThiLog)
                        Debug.Log($"Đã xóa {soLuongDaXoa}x {item.TenVatPham} khỏi inventory");

                    OnItemRemoved?.Invoke(item, soLuongDaXoa);
                    OnInventoryChanged?.Invoke();

                    if (tuDongLuu)
                        LuuDuLieu();
                }
            }

            return soLuongDaXoa;
        }

        /// <summary>
        /// Kiểm tra có vật phẩm trong inventory không
        /// </summary>
        /// <param name="itemID">ID vật phẩm</param>
        /// <param name="soLuong">Số lượng cần kiểm tra</param>
        /// <returns>True nếu có đủ</returns>
        public bool CoVatPham(string itemID, int soLuong = 1)
        {
            return LaySoLuongVatPham(itemID) >= soLuong;
        }

        /// <summary>
        /// Lấy số lượng vật phẩm trong inventory
        /// </summary>
        /// <param name="itemID">ID vật phẩm</param>
        /// <returns>Tổng số lượng</returns>
        public int LaySoLuongVatPham(string itemID)
        {
            return danhSachVatPham
                .Where(x => x.Item.ID == itemID)
                .Sum(x => x.SoLuong);
        }

        /// <summary>
        /// Lấy vật phẩm theo loại
        /// </summary>
        /// <param name="loai">Loại vật phẩm</param>
        /// <returns>Danh sách vật phẩm</returns>
        public List<InventoryItem> LayVatPhamTheoLoai(ItemType loai)
        {
            return danhSachVatPham
                .Where(x => x.Item.LoaiVatPham == loai)
                .ToList();
        }

        /// <summary>
        /// Sắp xếp inventory
        /// </summary>
        public void SapXepInventory()
        {
            danhSachVatPham = danhSachVatPham
                .OrderBy(x => x.Item.LoaiVatPham)
                .ThenBy(x => x.Item.DoHiem)
                .ThenBy(x => x.Item.TenVatPham)
                .ToList();

            OnInventoryChanged?.Invoke();

            if (hienThiLog)
                Debug.Log("Đã sắp xếp inventory");
        }

        /// <summary>
        /// Xóa toàn bộ inventory
        /// </summary>
        public void XoaToanBoInventory()
        {
            danhSachVatPham.Clear();
            OnInventoryChanged?.Invoke();

            if (tuDongLuu)
                LuuDuLieu();

            if (hienThiLog)
                Debug.Log("Đã xóa toàn bộ inventory");
        }

        /// <summary>
        /// Lưu dữ liệu inventory
        /// </summary>
        public void LuuDuLieu()
        {
            var inventoryData = new InventoryData();
            inventoryData.items = danhSachVatPham.ToList();

            string json = JsonUtility.ToJson(inventoryData, true);
            PlayerPrefs.SetString(INVENTORY_SAVE_KEY, json);
            PlayerPrefs.Save();

            if (hienThiLog)
                Debug.Log("Đã lưu dữ liệu inventory");
        }

        /// <summary>
        /// Tải dữ liệu inventory
        /// </summary>
        public void TaiDuLieu()
        {
            string json = PlayerPrefs.GetString(INVENTORY_SAVE_KEY, "");
            if (!string.IsNullOrEmpty(json))
            {
                try
                {
                    var inventoryData = JsonUtility.FromJson<InventoryData>(json);
                    danhSachVatPham = inventoryData.items ?? new List<InventoryItem>();

                    if (hienThiLog)
                        Debug.Log($"Đã tải dữ liệu inventory: {danhSachVatPham.Count} loại vật phẩm");
                }
                catch (System.Exception e)
                {
                    Debug.LogError($"Lỗi khi tải dữ liệu inventory: {e.Message}");
                    danhSachVatPham = new List<InventoryItem>();
                }
            }
        }

        /// <summary>
        /// Xóa dữ liệu inventory đã lưu
        /// </summary>
        public void XoaDuLieu()
        {
            PlayerPrefs.DeleteKey(INVENTORY_SAVE_KEY);
            PlayerPrefs.Save();
            danhSachVatPham.Clear();
            OnInventoryChanged?.Invoke();

            if (hienThiLog)
                Debug.Log("Đã xóa dữ liệu inventory");
        }
        #endregion

        #region Private Methods
        private Item GetItemByID(string itemID)
        {
            return danhSachVatPham.FirstOrDefault(x => x.Item.ID == itemID)?.Item;
        }
        #endregion

        #region Editor Methods
        #if UNITY_EDITOR
        [ContextMenu("Sắp Xếp Inventory")]
        private void SapXepInventoryTest()
        {
            SapXepInventory();
        }

        [ContextMenu("Xóa Toàn Bộ Inventory")]
        private void XoaToanBoInventoryTest()
        {
            XoaToanBoInventory();
        }

        [ContextMenu("Lưu Dữ Liệu")]
        private void LuuDuLieuTest()
        {
            LuuDuLieu();
        }

        [ContextMenu("Tải Dữ Liệu")]
        private void TaiDuLieuTest()
        {
            TaiDuLieu();
        }
        #endif
        #endregion
    }

    /// <summary>
    /// Class để serialize inventory data
    /// </summary>
    [System.Serializable]
    public class InventoryData
    {
        public List<InventoryItem> items = new List<InventoryItem>();
    }
}
