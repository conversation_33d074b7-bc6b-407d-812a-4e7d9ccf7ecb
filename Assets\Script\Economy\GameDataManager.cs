using System;
using System.Collections.Generic;
using UnityEngine;

namespace EconomySystem
{
    /// <summary>
    /// Class chứa dữ liệu theo ngày
    /// </summary>
    [System.Serializable]
    public class DailyData
    {
        [Header("Thông Tin Ngày")]
        public string ngayThang; // Format: yyyy-MM-dd
        public int soTienBatDau;
        public int soTienKetThuc;
        public int tongTienKiem;
        public int tongTienTieu;

        [Header("Hoạt Động")]
        public int soLanMuaHang;
        public int soLanBanHang;
        public List<string> vatPhamDaMua = new List<string>();
        public List<string> vatPhamDaBan = new List<string>();

        [Header("Thống Kê")]
        public int tongSoVatPhamThuThap;
        public int tongSoVatPhamSuDung;

        public DailyData()
        {
            ngayThang = DateTime.Now.ToString("yyyy-MM-dd");
            soTienBatDau = 0;
            soTienKetThuc = 0;
            tongTienKiem = 0;
            tongTienTieu = 0;
            soLanMuaHang = 0;
            soLanBanHang = 0;
            vatPhamDaMua = new List<string>();
            vatPhamDaBan = new List<string>();
            tongSoVatPhamThuThap = 0;
            tongSoVatPhamSuDung = 0;
        }

        public DailyData(string ngay)
        {
            ngayThang = ngay;
            soTienBatDau = 0;
            soTienKetThuc = 0;
            tongTienKiem = 0;
            tongTienTieu = 0;
            soLanMuaHang = 0;
            soLanBanHang = 0;
            vatPhamDaMua = new List<string>();
            vatPhamDaBan = new List<string>();
            tongSoVatPhamThuThap = 0;
            tongSoVatPhamSuDung = 0;
        }
    }

    /// <summary>
    /// Class chứa toàn bộ dữ liệu game
    /// </summary>
    [System.Serializable]
    public class GameData
    {
        [Header("Dữ Liệu Chung")]
        public int phienBanDuLieu = 1;
        public string ngayTaoFile;
        public string ngayCapNhatCuoi;

        [Header("Dữ Liệu Tiền Tệ")]
        public int soTienHienTai;
        public int tongTienKiemDuoc;
        public int tongTienDaTieu;

        [Header("Dữ Liệu Inventory")]
        public List<InventoryItem> inventory = new List<InventoryItem>();

        [Header("Dữ Liệu Theo Ngày")]
        public List<DailyData> duLieuTheoNgay = new List<DailyData>();

        [Header("Thống Kê Tổng")]
        public int tongSoNgayChoi;
        public int tongSoLanMuaHang;
        public int tongSoLanBanHang;

        public GameData()
        {
            phienBanDuLieu = 1;
            ngayTaoFile = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
            ngayCapNhatCuoi = ngayTaoFile;
            soTienHienTai = 100;
            tongTienKiemDuoc = 0;
            tongTienDaTieu = 0;
            inventory = new List<InventoryItem>();
            duLieuTheoNgay = new List<DailyData>();
            tongSoNgayChoi = 0;
            tongSoLanMuaHang = 0;
            tongSoLanBanHang = 0;
        }
    }

    /// <summary>
    /// Manager quản lý lưu trữ và tải dữ liệu game theo ngày
    /// Hỗ trợ persistent data storage
    /// </summary>
    public class GameDataManager : MonoBehaviour
    {
        #region Singleton
        private static GameDataManager _instance;
        public static GameDataManager Instance
        {
            get
            {
                if (_instance == null)
                {
                    _instance = FindObjectOfType<GameDataManager>();
                    if (_instance == null)
                    {
                        GameObject go = new GameObject("GameDataManager");
                        _instance = go.AddComponent<GameDataManager>();
                        DontDestroyOnLoad(go);
                    }
                }
                return _instance;
            }
        }
        #endregion

        #region Events
        /// <summary>
        /// Event khi dữ liệu được lưu
        /// </summary>
        public static event Action OnDataSaved;

        /// <summary>
        /// Event khi dữ liệu được tải
        /// </summary>
        public static event Action OnDataLoaded;

        /// <summary>
        /// Event khi bắt đầu ngày mới
        /// </summary>
        public static event Action<DailyData> OnNewDayStarted;
        #endregion

        #region Fields
        [Header("Cài Đặt")]
        [SerializeField] private bool tuDongLuu = true;
        [SerializeField] private float khoangThoiGianLuu = 60f; // Lưu mỗi 60 giây
        [SerializeField] private bool hienThiLog = true;
        [SerializeField] private bool taoBackup = true;

        [Header("Dữ Liệu Game")]
        [SerializeField] private GameData duLieuGame;
        [SerializeField] private DailyData duLieuHomNay;

        private const string GAME_DATA_KEY = "GameData";
        private const string LAST_PLAY_DATE_KEY = "LastPlayDate";
        private float thoiGianLuuCuoi;
        #endregion

        #region Properties
        /// <summary>
        /// Dữ liệu game hiện tại
        /// </summary>
        public GameData DuLieuGame => duLieuGame;

        /// <summary>
        /// Dữ liệu hôm nay
        /// </summary>
        public DailyData DuLieuHomNay => duLieuHomNay;

        /// <summary>
        /// Ngày hiện tại (format: yyyy-MM-dd)
        /// </summary>
        public string NgayHienTai => DateTime.Now.ToString("yyyy-MM-dd");
        #endregion

        #region Unity Methods
        private void Awake()
        {
            if (_instance == null)
            {
                _instance = this;
                DontDestroyOnLoad(gameObject);
                KhoiTaoDuLieu();
            }
            else if (_instance != this)
            {
                Destroy(gameObject);
            }
        }

        private void Start()
        {
            DangKyEvents();
            KiemTraNgayMoi();
        }

        private void Update()
        {
            if (tuDongLuu && Time.time - thoiGianLuuCuoi >= khoangThoiGianLuu)
            {
                LuuDuLieu();
                thoiGianLuuCuoi = Time.time;
            }
        }

        private void OnApplicationPause(bool pauseStatus)
        {
            if (pauseStatus)
                LuuDuLieu();
        }

        private void OnApplicationFocus(bool hasFocus)
        {
            if (!hasFocus)
                LuuDuLieu();
            else
                KiemTraNgayMoi();
        }

        private void OnDestroy()
        {
            HuyDangKyEvents();
            LuuDuLieu();
        }
        #endregion

        #region Initialization
        private void KhoiTaoDuLieu()
        {
            if (duLieuGame == null)
            {
                duLieuGame = new GameData();
            }

            TaiDuLieu();
            KiemTraNgayMoi();
        }

        private void DangKyEvents()
        {
            // Đăng ký events từ các manager khác
            if (CurrencyManager.Instance != null)
            {
                CurrencyManager.OnTransactionCompleted += OnTienThayDoi;
            }

            if (ShopManager.Instance != null)
            {
                ShopManager.OnItemPurchased += OnMuaVatPham;
                ShopManager.OnItemSold += OnBanVatPham;
            }

            if (InventoryManager.Instance != null)
            {
                InventoryManager.OnItemAdded += OnThemVatPham;
                InventoryManager.OnItemRemoved += OnXoaVatPham;
            }
        }

        private void HuyDangKyEvents()
        {
            CurrencyManager.OnTransactionCompleted -= OnTienThayDoi;
            ShopManager.OnItemPurchased -= OnMuaVatPham;
            ShopManager.OnItemSold -= OnBanVatPham;
            InventoryManager.OnItemAdded -= OnThemVatPham;
            InventoryManager.OnItemRemoved -= OnXoaVatPham;
        }
        #endregion

        #region Daily Data Management
        private void KiemTraNgayMoi()
        {
            string ngayHienTai = NgayHienTai;
            string ngayChoiCuoi = PlayerPrefs.GetString(LAST_PLAY_DATE_KEY, "");

            if (ngayChoiCuoi != ngayHienTai)
            {
                // Ngày mới
                BatDauNgayMoi(ngayHienTai);
                PlayerPrefs.SetString(LAST_PLAY_DATE_KEY, ngayHienTai);
                PlayerPrefs.Save();
            }
            else
            {
                // Cùng ngày, tải dữ liệu hôm nay
                duLieuHomNay = LayDuLieuTheoNgay(ngayHienTai);
            }
        }

        private void BatDauNgayMoi(string ngay)
        {
            // Lưu dữ liệu ngày cũ nếu có
            if (duLieuHomNay != null)
            {
                CapNhatDuLieuNgayCu();
            }

            // Tạo dữ liệu ngày mới
            duLieuHomNay = new DailyData(ngay);
            duLieuHomNay.soTienBatDau = CurrencyManager.Instance?.SoTienHienTai ?? 0;

            // Thêm vào danh sách
            duLieuGame.duLieuTheoNgay.Add(duLieuHomNay);
            duLieuGame.tongSoNgayChoi++;

            if (hienThiLog)
                Debug.Log($"Bắt đầu ngày mới: {ngay}");

            OnNewDayStarted?.Invoke(duLieuHomNay);
            LuuDuLieu();
        }

        private void CapNhatDuLieuNgayCu()
        {
            if (duLieuHomNay != null)
            {
                duLieuHomNay.soTienKetThuc = CurrencyManager.Instance?.SoTienHienTai ?? 0;
                duLieuGame.ngayCapNhatCuoi = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
            }
        }

        private DailyData LayDuLieuTheoNgay(string ngay)
        {
            var duLieu = duLieuGame.duLieuTheoNgay.Find(x => x.ngayThang == ngay);
            if (duLieu == null)
            {
                duLieu = new DailyData(ngay);
                duLieuGame.duLieuTheoNgay.Add(duLieu);
            }
            return duLieu;
        }
        #endregion

        #region Event Handlers
        private void OnTienThayDoi(int soTienThayDoi, string lyDo)
        {
            if (duLieuHomNay == null) return;

            if (soTienThayDoi > 0)
            {
                duLieuHomNay.tongTienKiem += soTienThayDoi;
                duLieuGame.tongTienKiemDuoc += soTienThayDoi;
            }
            else
            {
                duLieuHomNay.tongTienTieu += Mathf.Abs(soTienThayDoi);
                duLieuGame.tongTienDaTieu += Mathf.Abs(soTienThayDoi);
            }
        }

        private void OnMuaVatPham(Item item, int soLuong)
        {
            if (duLieuHomNay == null) return;

            duLieuHomNay.soLanMuaHang++;
            duLieuGame.tongSoLanMuaHang++;

            string thongTin = $"{item.TenVatPham} x{soLuong}";
            if (!duLieuHomNay.vatPhamDaMua.Contains(thongTin))
            {
                duLieuHomNay.vatPhamDaMua.Add(thongTin);
            }
        }

        private void OnBanVatPham(Item item, int soLuong)
        {
            if (duLieuHomNay == null) return;

            duLieuHomNay.soLanBanHang++;
            duLieuGame.tongSoLanBanHang++;

            string thongTin = $"{item.TenVatPham} x{soLuong}";
            if (!duLieuHomNay.vatPhamDaBan.Contains(thongTin))
            {
                duLieuHomNay.vatPhamDaBan.Add(thongTin);
            }
        }

        private void OnThemVatPham(Item item, int soLuong)
        {
            if (duLieuHomNay == null) return;
            duLieuHomNay.tongSoVatPhamThuThap += soLuong;
        }

        private void OnXoaVatPham(Item item, int soLuong)
        {
            if (duLieuHomNay == null) return;
            duLieuHomNay.tongSoVatPhamSuDung += soLuong;
        }
        #endregion

        #region Save/Load
        /// <summary>
        /// Lưu dữ liệu game
        /// </summary>
        public void LuuDuLieu()
        {
            try
            {
                CapNhatDuLieuTruocKhiLuu();

                string json = JsonUtility.ToJson(duLieuGame, true);
                PlayerPrefs.SetString(GAME_DATA_KEY, json);

                if (taoBackup)
                {
                    string backupKey = GAME_DATA_KEY + "_backup";
                    PlayerPrefs.SetString(backupKey, json);
                }

                PlayerPrefs.Save();

                if (hienThiLog)
                    Debug.Log("Đã lưu dữ liệu game");

                OnDataSaved?.Invoke();
            }
            catch (System.Exception e)
            {
                Debug.LogError($"Lỗi khi lưu dữ liệu: {e.Message}");
            }
        }

        /// <summary>
        /// Tải dữ liệu game
        /// </summary>
        public void TaiDuLieu()
        {
            try
            {
                string json = PlayerPrefs.GetString(GAME_DATA_KEY, "");
                
                if (!string.IsNullOrEmpty(json))
                {
                    duLieuGame = JsonUtility.FromJson<GameData>(json);
                    
                    if (hienThiLog)
                        Debug.Log("Đã tải dữ liệu game");
                }
                else
                {
                    duLieuGame = new GameData();
                    
                    if (hienThiLog)
                        Debug.Log("Tạo dữ liệu game mới");
                }

                OnDataLoaded?.Invoke();
            }
            catch (System.Exception e)
            {
                Debug.LogError($"Lỗi khi tải dữ liệu: {e.Message}");
                TaiDuLieuBackup();
            }
        }

        /// <summary>
        /// Tải dữ liệu backup
        /// </summary>
        public void TaiDuLieuBackup()
        {
            try
            {
                string backupKey = GAME_DATA_KEY + "_backup";
                string json = PlayerPrefs.GetString(backupKey, "");
                
                if (!string.IsNullOrEmpty(json))
                {
                    duLieuGame = JsonUtility.FromJson<GameData>(json);
                    Debug.Log("Đã tải dữ liệu backup");
                }
                else
                {
                    duLieuGame = new GameData();
                    Debug.Log("Tạo dữ liệu game mới từ backup");
                }
            }
            catch (System.Exception e)
            {
                Debug.LogError($"Lỗi khi tải backup: {e.Message}");
                duLieuGame = new GameData();
            }
        }

        /// <summary>
        /// Xóa tất cả dữ liệu
        /// </summary>
        public void XoaTatCaDuLieu()
        {
            PlayerPrefs.DeleteKey(GAME_DATA_KEY);
            PlayerPrefs.DeleteKey(GAME_DATA_KEY + "_backup");
            PlayerPrefs.DeleteKey(LAST_PLAY_DATE_KEY);
            PlayerPrefs.Save();

            duLieuGame = new GameData();
            duLieuHomNay = new DailyData();

            if (hienThiLog)
                Debug.Log("Đã xóa tất cả dữ liệu game");
        }

        private void CapNhatDuLieuTruocKhiLuu()
        {
            if (duLieuGame == null) return;

            // Cập nhật dữ liệu hiện tại
            duLieuGame.soTienHienTai = CurrencyManager.Instance?.SoTienHienTai ?? 0;
            duLieuGame.inventory = InventoryManager.Instance?.DanhSachVatPham ?? new List<InventoryItem>();
            duLieuGame.ngayCapNhatCuoi = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");

            // Cập nhật dữ liệu hôm nay
            if (duLieuHomNay != null)
            {
                duLieuHomNay.soTienKetThuc = duLieuGame.soTienHienTai;
            }
        }
        #endregion

        #region Public Methods
        /// <summary>
        /// Lấy thống kê theo ngày
        /// </summary>
        /// <param name="ngay">Ngày cần lấy (yyyy-MM-dd)</param>
        /// <returns>Dữ liệu ngày đó</returns>
        public DailyData LayThongKeTheoNgay(string ngay)
        {
            return duLieuGame.duLieuTheoNgay.Find(x => x.ngayThang == ngay);
        }

        /// <summary>
        /// Lấy thống kê 7 ngày gần nhất
        /// </summary>
        /// <returns>Danh sách dữ liệu 7 ngày</returns>
        public List<DailyData> LayThongKe7NgayGanNhat()
        {
            var result = new List<DailyData>();
            for (int i = 6; i >= 0; i--)
            {
                string ngay = DateTime.Now.AddDays(-i).ToString("yyyy-MM-dd");
                var duLieu = LayThongKeTheoNgay(ngay);
                if (duLieu != null)
                {
                    result.Add(duLieu);
                }
            }
            return result;
        }

        /// <summary>
        /// Lấy tổng thống kê
        /// </summary>
        /// <returns>Chuỗi thống kê</returns>
        public string LayTongThongKe()
        {
            return $"Tổng số ngày chơi: {duLieuGame.tongSoNgayChoi}\n" +
                   $"Tổng tiền kiếm được: {duLieuGame.tongTienKiemDuoc:N0} Lea\n" +
                   $"Tổng tiền đã tiêu: {duLieuGame.tongTienDaTieu:N0} Lea\n" +
                   $"Tổng số lần mua hàng: {duLieuGame.tongSoLanMuaHang}\n" +
                   $"Tổng số lần bán hàng: {duLieuGame.tongSoLanBanHang}";
        }
        #endregion

        #region Editor Methods
        #if UNITY_EDITOR
        [ContextMenu("Lưu Dữ Liệu")]
        private void LuuDuLieuTest()
        {
            LuuDuLieu();
        }

        [ContextMenu("Tải Dữ Liệu")]
        private void TaiDuLieuTest()
        {
            TaiDuLieu();
        }

        [ContextMenu("Xóa Tất Cả Dữ Liệu")]
        private void XoaTatCaDuLieuTest()
        {
            if (UnityEditor.EditorUtility.DisplayDialog("Xác nhận", "Bạn có chắc muốn xóa tất cả dữ liệu?", "Có", "Không"))
            {
                XoaTatCaDuLieu();
            }
        }

        [ContextMenu("Hiển Thị Thống Kê")]
        private void HienThiThongKeTest()
        {
            Debug.Log(LayTongThongKe());
        }
        #endif
        #endregion
    }
}
