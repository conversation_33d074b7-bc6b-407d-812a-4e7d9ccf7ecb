using System;
using UnityEngine;

namespace EconomySystem
{
    /// <summary>
    /// Manager quản lý tiền tệ Lea trong game với Singleton pattern
    /// Hỗ trợ lưu trữ persistent và events
    /// </summary>
    public class CurrencyManager : MonoBehaviour
    {
        #region Singleton
        private static CurrencyManager _instance;
        public static CurrencyManager Instance
        {
            get
            {
                if (_instance == null)
                {
                    _instance = FindObjectOfType<CurrencyManager>();
                    if (_instance == null)
                    {
                        GameObject go = new GameObject("CurrencyManager");
                        _instance = go.AddComponent<CurrencyManager>();
                        DontDestroyOnLoad(go);
                    }
                }
                return _instance;
            }
        }
        #endregion

        #region Events
        /// <summary>
        /// Event khi số tiền thay đổi (số tiền mới)
        /// </summary>
        public static event Action<int> OnCurrencyChanged;

        /// <summary>
        /// Event khi giao dịch hoàn thành (số tiền thay đổi, lý do)
        /// </summary>
        public static event Action<int, string> OnTransactionCompleted;

        /// <summary>
        /// Event khi giao dịch thất bại (lý do)
        /// </summary>
        public static event Action<string> OnTransactionFailed;
        #endregion

        #region Fields
        [Header("Cài Đặt Tiền Tệ")]
        [SerializeField] private int soTienHienTai = 100;
        [SerializeField] private int soTienToiDa = 999999;
        [SerializeField] private bool tuDongLuu = true;
        [SerializeField] private bool hienThiLog = true;

        [Header("Hiệu Ứng")]
        [SerializeField] private bool coHieuUng = true;
        [SerializeField] private float thoiGianHieuUng = 0.5f;

        private const string CURRENCY_SAVE_KEY = "PlayerCurrency";
        #endregion

        #region Properties
        /// <summary>
        /// Số tiền hiện tại của người chơi
        /// </summary>
        public int SoTienHienTai 
        { 
            get => soTienHienTai; 
            private set 
            { 
                soTienHienTai = Mathf.Clamp(value, 0, soTienToiDa);
                OnCurrencyChanged?.Invoke(soTienHienTai);
                
                if (tuDongLuu)
                    LuuDuLieu();
            } 
        }

        /// <summary>
        /// Số tiền tối đa có thể có
        /// </summary>
        public int SoTienToiDa => soTienToiDa;

        /// <summary>
        /// Kiểm tra có đạt giới hạn tiền không
        /// </summary>
        public bool DaDatGioiHan => soTienHienTai >= soTienToiDa;
        #endregion

        #region Unity Methods
        private void Awake()
        {
            if (_instance == null)
            {
                _instance = this;
                DontDestroyOnLoad(gameObject);
                TaiDuLieu();
            }
            else if (_instance != this)
            {
                Destroy(gameObject);
            }
        }

        private void Start()
        {
            // Trigger event để UI cập nhật
            OnCurrencyChanged?.Invoke(soTienHienTai);
        }

        private void OnApplicationPause(bool pauseStatus)
        {
            if (pauseStatus && tuDongLuu)
                LuuDuLieu();
        }

        private void OnApplicationFocus(bool hasFocus)
        {
            if (!hasFocus && tuDongLuu)
                LuuDuLieu();
        }

        private void OnDestroy()
        {
            if (tuDongLuu)
                LuuDuLieu();
        }
        #endregion

        #region Public Methods
        /// <summary>
        /// Thêm tiền Lea
        /// </summary>
        /// <param name="soTien">Số tiền cần thêm</param>
        /// <param name="lyDo">Lý do thêm tiền</param>
        /// <returns>Số tiền thực tế đã thêm</returns>
        public int ThemTien(int soTien, string lyDo = "")
        {
            if (soTien <= 0)
            {
                if (hienThiLog)
                    Debug.LogWarning("Không thể thêm số tiền âm hoặc bằng 0");
                return 0;
            }

            int soTienCu = soTienHienTai;
            int soTienMoi = Mathf.Min(soTienHienTai + soTien, soTienToiDa);
            int soTienThucTeThemVao = soTienMoi - soTienCu;

            SoTienHienTai = soTienMoi;

            if (hienThiLog)
                Debug.Log($"Đã thêm {soTienThucTeThemVao} Lea. Lý do: {lyDo}. Tổng: {soTienHienTai} Lea");

            OnTransactionCompleted?.Invoke(soTienThucTeThemVao, lyDo);

            if (soTienThucTeThemVao < soTien && hienThiLog)
                Debug.LogWarning($"Chỉ có thể thêm {soTienThucTeThemVao}/{soTien} Lea do đạt giới hạn");

            return soTienThucTeThemVao;
        }

        /// <summary>
        /// Trừ tiền Lea
        /// </summary>
        /// <param name="soTien">Số tiền cần trừ</param>
        /// <param name="lyDo">Lý do trừ tiền</param>
        /// <returns>True nếu thành công, False nếu không đủ tiền</returns>
        public bool TruTien(int soTien, string lyDo = "")
        {
            if (soTien <= 0)
            {
                if (hienThiLog)
                    Debug.LogWarning("Không thể trừ số tiền âm hoặc bằng 0");
                return false;
            }

            if (!KiemTraDuTien(soTien))
            {
                if (hienThiLog)
                    Debug.LogWarning($"Không đủ tiền! Cần {soTien} Lea, chỉ có {soTienHienTai} Lea");
                
                OnTransactionFailed?.Invoke($"Không đủ tiền để {lyDo}");
                return false;
            }

            SoTienHienTai -= soTien;

            if (hienThiLog)
                Debug.Log($"Đã trừ {soTien} Lea. Lý do: {lyDo}. Còn lại: {soTienHienTai} Lea");

            OnTransactionCompleted?.Invoke(-soTien, lyDo);
            return true;
        }

        /// <summary>
        /// Kiểm tra có đủ tiền không
        /// </summary>
        /// <param name="soTien">Số tiền cần kiểm tra</param>
        /// <returns>True nếu đủ tiền</returns>
        public bool KiemTraDuTien(int soTien)
        {
            return soTienHienTai >= soTien;
        }

        /// <summary>
        /// Đặt lại số tiền (chỉ dùng cho debug/admin)
        /// </summary>
        /// <param name="soTienMoi">Số tiền mới</param>
        public void DatLaiSoTien(int soTienMoi)
        {
            SoTienHienTai = Mathf.Clamp(soTienMoi, 0, soTienToiDa);
            
            if (hienThiLog)
                Debug.Log($"Đã đặt lại số tiền thành {soTienHienTai} Lea");

            OnTransactionCompleted?.Invoke(0, "Đặt lại số tiền");
        }

        /// <summary>
        /// Lưu dữ liệu tiền tệ
        /// </summary>
        public void LuuDuLieu()
        {
            PlayerPrefs.SetInt(CURRENCY_SAVE_KEY, soTienHienTai);
            PlayerPrefs.Save();

            if (hienThiLog)
                Debug.Log($"Đã lưu dữ liệu tiền tệ: {soTienHienTai} Lea");
        }

        /// <summary>
        /// Tải dữ liệu tiền tệ
        /// </summary>
        public void TaiDuLieu()
        {
            soTienHienTai = PlayerPrefs.GetInt(CURRENCY_SAVE_KEY, soTienHienTai);
            
            if (hienThiLog)
                Debug.Log($"Đã tải dữ liệu tiền tệ: {soTienHienTai} Lea");
        }

        /// <summary>
        /// Xóa dữ liệu tiền tệ đã lưu
        /// </summary>
        public void XoaDuLieu()
        {
            PlayerPrefs.DeleteKey(CURRENCY_SAVE_KEY);
            PlayerPrefs.Save();
            soTienHienTai = 100; // Reset về giá trị mặc định
            
            if (hienThiLog)
                Debug.Log("Đã xóa dữ liệu tiền tệ");

            OnCurrencyChanged?.Invoke(soTienHienTai);
        }

        /// <summary>
        /// Lấy thông tin chi tiết về tiền tệ
        /// </summary>
        public string LayThongTinChiTiet()
        {
            return $"Tiền hiện tại: {soTienHienTai:N0} Lea\n" +
                   $"Giới hạn: {soTienToiDa:N0} Lea\n" +
                   $"Phần trăm đạt được: {(float)soTienHienTai / soTienToiDa * 100:F1}%";
        }
        #endregion

        #region Editor Methods
        #if UNITY_EDITOR
        [ContextMenu("Thêm 1000 Lea")]
        private void ThemTienTest()
        {
            ThemTien(1000, "Test thêm tiền");
        }

        [ContextMenu("Trừ 500 Lea")]
        private void TruTienTest()
        {
            TruTien(500, "Test trừ tiền");
        }

        [ContextMenu("Reset Tiền")]
        private void ResetTienTest()
        {
            DatLaiSoTien(100);
        }

        [ContextMenu("Lưu Dữ Liệu")]
        private void LuuDuLieuTest()
        {
            LuuDuLieu();
        }

        [ContextMenu("Tải Dữ Liệu")]
        private void TaiDuLieuTest()
        {
            TaiDuLieu();
        }

        [ContextMenu("Xóa Dữ Liệu")]
        private void XoaDuLieuTest()
        {
            XoaDuLieu();
        }
        #endif
        #endregion
    }
}
